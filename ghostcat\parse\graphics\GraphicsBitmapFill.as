package ghostcat.parse.graphics
{
   import flash.display.BitmapData;
   import flash.display.Graphics;
   import flash.geom.Matrix;
   import ghostcat.parse.DisplayParse;
   
   public class GraphicsBitmapFill extends DisplayParse implements IGraphicsFill
   {
      
      public var bitmap:BitmapData;
      
      public var matrix:Matrix;
      
      public var repeat:<PERSON><PERSON>an;
      
      public var smooth:<PERSON><PERSON><PERSON>;
      
      public function GraphicsBitmapFill(param1:BitmapData, param2:Matrix = null, param3:Boolean = true, param4:Boolean = false)
      {
         super();
         this.bitmap = param1;
         this.matrix = param2;
         this.repeat = param3;
         this.smooth = param4;
      }
      
      public static function drawBitmpData(param1:Graphics, param2:BitmapData, param3:Number, param4:Number, param5:Number = NaN, param6:Number = NaN) : void
      {
         var _loc7_:Matrix = null;
         var _loc8_:Number = NaN;
         var _loc9_:Number = NaN;
         if(param2)
         {
            _loc7_ = new Matrix();
            _loc7_.translate(param3,param4);
            if(isNaN(param5))
            {
               param5 = param2.width;
            }
            if(isNaN(param6))
            {
               param6 = param2.height;
            }
            _loc8_ = param5 / param2.width;
            _loc9_ = param6 / param2.height;
            _loc7_.scale(_loc8_,_loc9_);
            param1.beginBitmapFill(param2,_loc7_,false,false);
            param1.drawRect(param3,param4,param5,param6);
            param1.endFill();
         }
      }
      
      override public function parseGraphics(param1:Graphics) : void
      {
         super.parseGraphics(param1);
         param1.beginBitmapFill(this.bitmap,this.matrix,this.repeat,this.smooth);
      }
   }
}

