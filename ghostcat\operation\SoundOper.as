package ghostcat.operation
{
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.media.Sound;
   import flash.media.SoundChannel;
   import flash.media.SoundLoaderContext;
   import flash.media.SoundTransform;
   import flash.net.URLRequest;
   import flash.utils.getDefinitionByName;
   import ghostcat.debug.Debug;
   
   public class SoundOper extends Oper
   {
      
      public static var urlBase:String = "";
      
      public var source:*;
      
      public var channel:SoundChannel;
      
      public var startTime:int;
      
      public var loops:int;
      
      public var playWhenComplete:Boolean;
      
      public var tweenQueue:Queue;
      
      private var _soundTransform:SoundTransform;
      
      public function SoundOper(param1:* = null, param2:Boolean = true, param3:int = 0, param4:int = 1, param5:Number = 1, param6:Number = 0.5)
      {
         super();
         this.source = param1;
         this.playWhenComplete = param2;
         this.startTime = param3;
         this.loops = param4;
         this.soundTransform = new SoundTransform(param5,param6);
      }
      
      public function set soundTransform(param1:SoundTransform) : void
      {
         if(this.channel)
         {
            this.channel.soundTransform = param1;
         }
         else
         {
            this._soundTransform = param1;
         }
      }
      
      public function get soundTransform() : SoundTransform
      {
         if(this.channel)
         {
            return this.channel.soundTransform;
         }
         return this._soundTransform;
      }
      
      public function addTween(param1:int = 1000, param2:Number = NaN, param3:Number = NaN, param4:Function = null) : void
      {
         if(!this.tweenQueue)
         {
            this.tweenQueue = new Queue();
         }
         var _loc5_:Object = new Object();
         if(!isNaN(param2))
         {
            _loc5_.volume = param2;
         }
         if(!isNaN(param3))
         {
            _loc5_.pan = param3;
         }
         if(param4 != null)
         {
            _loc5_.ease = param4;
         }
         this.tweenQueue.children.push(new TweenOper(this,param1,_loc5_));
      }
      
      override public function execute() : void
      {
         var s:Sound = null;
         super.execute();
         if(this.source is String)
         {
            try
            {
               this.source = getDefinitionByName(this.source);
            }
            catch(e:Error)
            {
            }
         }
         if(this.source is Class)
         {
            this.source = new this.source();
         }
         if(!(this.source is Sound))
         {
            s = new Sound();
            s.addEventListener(IOErrorEvent.IO_ERROR,this.fault);
            s.addEventListener(Event.COMPLETE,this.loadSoundComplete);
            s.load(new URLRequest(urlBase + this.source),new SoundLoaderContext(1000,true));
            this.source = s;
            if(this.playWhenComplete)
            {
               return;
            }
         }
         if(this.source is Sound)
         {
            this.playSound(this.source as Sound);
         }
         else
         {
            Debug.error("数据源格式错误");
         }
      }
      
      private function loadSoundComplete(param1:Event) : void
      {
         param1.currentTarget.removeEventListener(IOErrorEvent.IO_ERROR,this.fault);
         param1.currentTarget.removeEventListener(Event.COMPLETE,this.loadSoundComplete);
         dispatchEvent(param1);
         if(this.playWhenComplete)
         {
            this.playSound(this.source as Sound);
         }
      }
      
      protected function playSound(param1:Sound) : SoundChannel
      {
         this.channel = param1.play(this.startTime,this.loops >= 0 ? this.loops : int.MAX_VALUE,this.soundTransform);
         this.channel.addEventListener(Event.SOUND_COMPLETE,this.result);
         if(this.tweenQueue)
         {
            this.tweenQueue.execute();
         }
         return this.channel;
      }
      
      override public function result(param1:* = null) : void
      {
         this.channel.removeEventListener(Event.SOUND_COMPLETE,this.result);
         super.result(param1);
      }
      
      override public function fault(param1:* = null) : void
      {
         param1.currentTarget.removeEventListener(IOErrorEvent.IO_ERROR,this.fault);
         param1.currentTarget.removeEventListener(Event.COMPLETE,this.loadSoundComplete);
         if(this.channel)
         {
            this.channel.removeEventListener(Event.SOUND_COMPLETE,this.result);
         }
         super.result(param1);
      }
   }
}

