package ghostcat.parse.display
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.IBitmapDrawable;
   import flash.geom.ColorTransform;
   import flash.geom.Matrix;
   import flash.geom.Rectangle;
   import ghostcat.debug.Debug;
   import ghostcat.parse.DisplayParse;
   
   public class DrawParse extends DisplayParse
   {
      
      public var source:IBitmapDrawable;
      
      public var matrix:Matrix = null;
      
      public var colorTransform:ColorTransform = null;
      
      public var blendMode:String = null;
      
      public var clipRect:Rectangle = null;
      
      public var smoothing:Boolean = false;
      
      public var transparent:Boolean = true;
      
      public var fillColor:uint = 16777215;
      
      public function DrawParse(param1:IBitmapDrawable, param2:Matrix = null, param3:ColorTransform = null, param4:Rectangle = null, param5:Boolean = false, param6:Boolean = true, param7:uint = 16777215)
      {
         super();
         this.source = param1;
         this.matrix = param2;
         this.colorTransform = param3;
         this.blendMode = this.blendMode;
         this.clipRect = param4;
         this.smoothing = param5;
         this.transparent = param6;
         this.fillColor = param7;
      }
      
      public static function createBitmap(param1:IBitmapDrawable, param2:Matrix = null, param3:ColorTransform = null, param4:Rectangle = null, param5:Boolean = false) : Bitmap
      {
         return new DrawParse(param1,param2,param3,param4,param5).createBitmap();
      }
      
      override public function parse(param1:*) : void
      {
         if(param1 is Bitmap)
         {
            param1 = (param1 as Bitmap).bitmapData;
         }
         super.parse(param1);
      }
      
      override public function parseBitmapData(param1:BitmapData) : void
      {
         var target:BitmapData = param1;
         super.parseBitmapData(target);
         try
         {
            target.draw(this.source,this.matrix,this.colorTransform,this.blendMode,this.clipRect,this.smoothing);
         }
         catch(e:Error)
         {
            Debug.error(e.message);
         }
      }
      
      public function createBitmap() : Bitmap
      {
         var bounds:Rectangle;
         var width:int;
         var height:int;
         var bitmap:Bitmap = null;
         var displayObj:DisplayObject = this.source as DisplayObject;
         if(!displayObj)
         {
            return null;
         }
         bounds = displayObj.getBounds(displayObj);
         width = Math.ceil(bounds.width);
         height = Math.ceil(bounds.height);
         if(!this.matrix)
         {
            this.matrix = new Matrix();
            this.matrix.tx -= bounds.x;
            this.matrix.ty -= bounds.y;
         }
         try
         {
            bitmap = new Bitmap(new BitmapData(width,height,this.transparent,this.fillColor));
         }
         catch(e:Error)
         {
            Debug.error(e.message);
            bitmap = new Bitmap();
         }
         if(this.source is DisplayObject)
         {
            bitmap.x = (this.source as DisplayObject).x + bounds.x;
            bitmap.y = (this.source as DisplayObject).y + bounds.y;
         }
         this.parse(bitmap);
         return bitmap;
      }
      
      public function createBitmapData() : BitmapData
      {
         return this.createBitmap().bitmapData;
      }
   }
}

