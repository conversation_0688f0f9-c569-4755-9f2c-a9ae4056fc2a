package ghostcat.skin.cursor
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol210")]
   public dynamic class CursorGroup extends MovieClip
   {
      
      public var point:CursorPoint;
      
      public var vhDrag:CursorVHDrag;
      
      public var arow:CursorArow;
      
      public var rotateTopLeft:CursorRotateTopLeft;
      
      public var hDrag:CursorHDrag;
      
      public var drag:CursorDrag;
      
      public var busy:CursorBusy;
      
      public var hvDrag:CursorHVDrag;
      
      public var rotateTopRight:CursorRotateTopRight;
      
      public var rotateBottomRight:CursorRotateBottomRight;
      
      public var vDrag:CursorVDrag;
      
      public var rotateBottomLeft:CursorRotateBottomLeft;
      
      public function CursorGroup()
      {
         super();
      }
   }
}

