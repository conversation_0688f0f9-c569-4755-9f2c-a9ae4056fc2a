package ghostcat.text
{
   import flash.display.Bitmap;
   import flash.display.DisplayObjectContainer;
   import flash.geom.Matrix;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   import flash.text.TextLineMetrics;
   import ghostcat.parse.display.DrawParse;
   import ghostcat.util.display.MatrixUtil;
   
   public final class TextFieldUtil
   {
      
      public static const TEXT_WIDTH_PADDING:int = 5;
      
      public static const TEXT_HEIGHT_PADDING:int = 4;
      
      public function TextFieldUtil()
      {
         super();
      }
      
      public static function adjustSize(param1:TextField, param2:Number = 1000, param3:Number = 1000) : void
      {
         param1.width = param2;
         param1.height = param3;
         param1.width = Math.ceil(param1.textWidth) + TEXT_WIDTH_PADDING;
         param1.height = Math.ceil(param1.textHeight) + TEXT_HEIGHT_PADDING;
      }
      
      public static function truncateToFit(param1:TextField) : *
      {
         var _loc2_:String = param1.text;
         if(_loc2_ == null || _loc2_.length == 0)
         {
            return;
         }
         var _loc3_:TextLineMetrics = param1.getLineMetrics(0);
         while(_loc3_.width > param1.width)
         {
            _loc2_ = _loc2_.slice(0,_loc2_.length - 1);
            param1.text = _loc2_ + "...";
            _loc3_ = param1.getLineMetrics(0);
         }
      }
      
      public static function getTextFieldAtIndex(param1:TextField, param2:int) : TextField
      {
         var _loc3_:TextField = new TextField();
         _loc3_.selectable = false;
         _loc3_.embedFonts = param1.embedFonts;
         _loc3_.text = param1.text.charAt(param2);
         _loc3_.setTextFormat(param1.getTextFormat(param2,param2 + 1),0,1);
         var _loc4_:Rectangle = param1.getCharBoundaries(param2);
         var _loc5_:Rectangle = _loc3_.getCharBoundaries(0);
         if(Boolean(_loc4_) && Boolean(_loc5_))
         {
            _loc3_.x = _loc4_.x - _loc5_.x;
            _loc3_.y = _loc4_.y - _loc5_.y;
         }
         return _loc3_;
      }
      
      public static function separate(param1:TextField, param2:DisplayObjectContainer = null, param3:Boolean = false) : Array
      {
         var _loc7_:TextField = null;
         var _loc8_:Bitmap = null;
         if(!param2)
         {
            param2 = param1.parent;
         }
         var _loc4_:Matrix = MatrixUtil.getMatrixAt(param1,param2);
         var _loc5_:Array = [];
         var _loc6_:int = 0;
         while(_loc6_ < param1.text.length)
         {
            _loc7_ = getTextFieldAtIndex(param1,_loc6_);
            _loc7_.transform.matrix = MatrixUtil.concat(_loc4_,_loc7_.transform.matrix);
            _loc7_.transform.colorTransform = param1.transform.colorTransform;
            _loc7_.filters = param1.filters;
            if(param3)
            {
               _loc8_ = DrawParse.createBitmap(_loc7_);
               param2.addChild(_loc8_);
               _loc5_.push(_loc8_);
            }
            else
            {
               param2.addChild(_loc7_);
               _loc5_.push(_loc7_);
            }
            _loc6_++;
         }
         return _loc5_;
      }
      
      public static function clone(param1:TextField, param2:Boolean = false) : TextField
      {
         var _loc5_:DisplayObjectContainer = null;
         var _loc6_:int = 0;
         var _loc3_:TextField = new TextField();
         _loc3_.autoSize = param1.autoSize;
         _loc3_.embedFonts = param1.embedFonts;
         _loc3_.defaultTextFormat = param1.defaultTextFormat;
         _loc3_.text = param1.text;
         var _loc4_:int = 0;
         while(_loc4_ < param1.text.length)
         {
            _loc3_.setTextFormat(param1.getTextFormat(_loc4_,_loc4_ + 1),_loc4_,_loc4_ + 1);
            _loc4_++;
         }
         _loc3_.x = param1.x;
         _loc3_.y = param1.y;
         _loc3_.scaleX = param1.scaleX;
         _loc3_.scaleY = param1.scaleY;
         _loc3_.width = param1.width;
         _loc3_.height = param1.height;
         _loc3_.rotation = param1.rotation;
         _loc3_.multiline = param1.multiline;
         _loc3_.selectable = param1.selectable;
         _loc3_.wordWrap = param1.wordWrap;
         _loc3_.transform.colorTransform = param1.transform.colorTransform;
         _loc3_.filters = param1.filters;
         if(param2 && Boolean(param1.parent))
         {
            _loc5_ = param1.parent;
            _loc6_ = _loc5_.getChildIndex(param1);
            _loc5_.removeChild(param1);
            _loc5_.addChildAt(_loc3_,_loc6_);
         }
         return _loc3_;
      }
      
      public static function getLineHtmlText(param1:TextField, param2:int) : String
      {
         var _loc3_:int = param1.getLineOffset(param2);
         var _loc4_:int = param1.getLineLength(param2);
         var _loc5_:TextField = new TextField();
         var _loc6_:int = 0;
         while(_loc6_ < _loc4_)
         {
            _loc5_.appendText(param1.text.charAt(_loc3_ + _loc6_));
            _loc5_.setTextFormat(param1.getTextFormat(_loc3_ + _loc6_,_loc3_ + _loc6_ + 1),_loc6_,_loc6_ + 1);
            _loc6_++;
         }
         return _loc5_.htmlText;
      }
   }
}

