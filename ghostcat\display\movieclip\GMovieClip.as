package ghostcat.display.movieclip
{
   import flash.display.MovieClip;
   import flash.geom.Rectangle;
   
   public class GM<PERSON>ie<PERSON><PERSON> extends GMovieClipBase
   {
      
      public var timeLine:TimeLine;
      
      public function GMovieClip(param1:* = null, param2:<PERSON>olean = true, param3:<PERSON><PERSON><PERSON> = false)
      {
         super(param1,param2,param3);
      }
      
      public function get mc() : MovieClip
      {
         return content as MovieClip;
      }
      
      override public function setContent(param1:*, param2:<PERSON>olean = true) : void
      {
         super.setContent(param1,param2);
         if(this.mc)
         {
            this.timeLine = new TimeLine(this.mc);
            this.mc.stop();
            reset();
         }
      }
      
      override public function get curLabelName() : String
      {
         return this.timeLine.curLabelName;
      }
      
      override public function getLabelIndex(param1:String) : int
      {
         return this.timeLine.getLabelIndex(param1);
      }
      
      override public function get labels() : Array
      {
         return this.timeLine ? this.timeLine.labels : null;
      }
      
      override public function set currentFrame(param1:int) : void
      {
         if(param1 < 1)
         {
            param1 = 1;
         }
         if(param1 > this.totalFrames)
         {
            param1 = this.totalFrames;
         }
         if(currentFrame == param1)
         {
            return;
         }
         if(this.mc)
         {
            if(param1 == currentFrame + 1)
            {
               this.mc.nextFrame();
            }
            else if(param1 == currentFrame - 1)
            {
               this.mc.prevFrame();
            }
            else
            {
               this.mc.gotoAndStop(param1);
            }
         }
         super.currentFrame = param1;
      }
      
      override public function get totalFrames() : int
      {
         return this.mc ? this.mc.totalFrames : 0;
      }
      
      public function toGBitmapMovieClip(param1:Rectangle = null, param2:int = 1, param3:int = -1, param4:Boolean = false) : GBitmapMovieClip
      {
         var _loc5_:GBitmapMovieClip = new GBitmapMovieClip();
         _loc5_.createFromMovieClip(this.mc,param1,param2,param3,param4);
         return _loc5_;
      }
   }
}

