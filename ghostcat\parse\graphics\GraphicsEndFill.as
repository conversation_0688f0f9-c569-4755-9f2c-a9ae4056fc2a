package ghostcat.parse.graphics
{
   import flash.display.Graphics;
   import ghostcat.parse.DisplayParse;
   
   public class GraphicsEndFill extends DisplayParse
   {
      
      public function GraphicsEndFill()
      {
         super();
      }
      
      override public function parseGraphics(param1:Graphics) : void
      {
         super.parseGraphics(param1);
         param1.endFill();
      }
   }
}

