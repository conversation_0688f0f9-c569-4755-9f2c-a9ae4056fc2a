package ghostcat.util.display
{
   import flash.display.DisplayObject;
   import flash.geom.Matrix;
   
   public final class MatrixUtil
   {
      
      public function MatrixUtil()
      {
         super();
      }
      
      public static function getMatrixAt(param1:DisplayObject, param2:DisplayObject) : Matrix
      {
         if(param1 == param2)
         {
            return new Matrix();
         }
         var _loc3_:Matrix = param1.stage ? param1.transform.concatenatedMatrix : param1.transform.matrix;
         var _loc4_:Matrix = param2.stage ? param2.transform.concatenatedMatrix : param2.transform.matrix;
         _loc4_.invert();
         _loc3_.concat(_loc4_);
         return _loc3_;
      }
      
      public static function createBox(param1:Number, param2:Number, param3:Number = 0, param4:Number = 0, param5:Number = 0) : Matrix
      {
         var _loc6_:Matrix = new Matrix();
         _loc6_.createBox(param1,param2,param3,param4,param5);
         return _loc6_;
      }
      
      public static function createGradientBox(param1:Number, param2:Number, param3:Number = 0, param4:Number = 0, param5:Number = 0) : Matrix
      {
         var _loc6_:Matrix = new Matrix();
         _loc6_.createGradientBox(param1,param2,param3,param4,param5);
         return _loc6_;
      }
      
      public static function concat(param1:Matrix, param2:Matrix) : Matrix
      {
         var _loc3_:Matrix = param1.clone();
         _loc3_.concat(param2);
         return _loc3_;
      }
      
      public static function invert(param1:Matrix) : Matrix
      {
         param1 = param1.clone();
         param1.invert();
         return param1;
      }
   }
}

