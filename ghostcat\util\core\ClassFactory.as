package ghostcat.util.core
{
   import flash.utils.getDefinitionByName;
   
   public class ClassFactory
   {
      
      private static const applyFuns:Array = [apply0,apply1,apply2,apply3,apply4,apply5,apply6,apply7,apply8,apply9,apply10,apply11,apply12];
      
      public var generator:*;
      
      public var properties:Object = null;
      
      public var params:Array = null;
      
      public function ClassFactory(param1:* = null, param2:Object = null, param3:Array = null)
      {
         super();
         this.generator = param1;
         this.properties = param2;
         this.params = param3;
      }
      
      public static function apply(param1:Class, param2:Array = null) : *
      {
         if(param2)
         {
            return (applyFuns[param2.length] as Function)(param1,param2);
         }
         return new param1();
      }
      
      private static function apply0(param1:Class, param2:Array) : *
      {
         return new param1();
      }
      
      private static function apply1(param1:Class, param2:Array) : *
      {
         return new param1(param2[0]);
      }
      
      private static function apply2(param1:Class, param2:Array) : *
      {
         return new param1(param2[0],param2[1]);
      }
      
      private static function apply3(param1:Class, param2:Array) : *
      {
         return new param1(param2[0],param2[1],param2[2]);
      }
      
      private static function apply4(param1:Class, param2:Array) : *
      {
         return new param1(param2[0],param2[1],param2[2],param2[3]);
      }
      
      private static function apply5(param1:Class, param2:Array) : *
      {
         return new param1(param2[0],param2[1],param2[2],param2[3],param2[4]);
      }
      
      private static function apply6(param1:Class, param2:Array) : *
      {
         return new param1(param2[0],param2[1],param2[2],param2[3],param2[4],param2[5]);
      }
      
      private static function apply7(param1:Class, param2:Array) : *
      {
         return new param1(param2[0],param2[1],param2[2],param2[3],param2[4],param2[5],param2[6]);
      }
      
      private static function apply8(param1:Class, param2:Array) : *
      {
         return new param1(param2[0],param2[1],param2[2],param2[3],param2[4],param2[5],param2[6],param2[7]);
      }
      
      private static function apply9(param1:Class, param2:Array) : *
      {
         return new param1(param2[0],param2[1],param2[2],param2[3],param2[4],param2[5],param2[6],param2[7],param2[8]);
      }
      
      private static function apply10(param1:Class, param2:Array) : *
      {
         return new param1(param2[0],param2[1],param2[2],param2[3],param2[4],param2[5],param2[6],param2[7],param2[8],param2[9]);
      }
      
      private static function apply11(param1:Class, param2:Array) : *
      {
         return new param1(param2[0],param2[1],param2[2],param2[3],param2[4],param2[5],param2[6],param2[7],param2[8],param2[9],param2[10]);
      }
      
      private static function apply12(param1:Class, param2:Array) : *
      {
         return new param1(param2[0],param2[1],param2[2],param2[3],param2[4],param2[5],param2[6],param2[7],param2[8],param2[9],param2[10],param2[11]);
      }
      
      public function setProperties(param1:String, param2:*) : void
      {
         if(!this.properties)
         {
            this.properties = new Object();
         }
         this.properties[param1] = param2;
      }
      
      public function newInstance() : *
      {
         var _loc1_:* = undefined;
         var _loc2_:String = null;
         if(!this.generator)
         {
            return null;
         }
         if(this.generator is String)
         {
            this.generator = getDefinitionByName(this.generator) as Class;
         }
         if(Boolean(this.params) && this.params.length > 0)
         {
            _loc1_ = (applyFuns[this.params.length] as Function)(this.generator,this.params);
         }
         else
         {
            _loc1_ = new this.generator();
         }
         if(this.properties)
         {
            for(_loc2_ in this.properties)
            {
               if(_loc1_.hasOwnProperty(_loc2_))
               {
                  _loc1_[_loc2_] = this.properties[_loc2_];
               }
            }
         }
         return _loc1_;
      }
      
      public function isClass(param1:*) : Boolean
      {
         if(!this.generator)
         {
            return false;
         }
         if(this.generator is String)
         {
            this.generator = getDefinitionByName(this.generator) as Class;
         }
         return param1 is this.generator;
      }
   }
}

