package cutShoter
{
   import asfiles.encoding.*;
   import com.adobe.crypto.*;
   import cutShoter.events.*;
   import cutShoter.view.mc_completed;
   import cutShoter.view.mc_editContent;
   import cutShoter.view.mc_submiting;
   import flash.display.*;
   import flash.errors.*;
   import flash.events.*;
   import flash.external.*;
   import flash.media.*;
   import flash.net.*;
   import flash.system.*;
   import flash.utils.*;
   import ghostcat.display.screenshot.*;
   import ghostcat.ui.*;
   import swfParser.*;
   
   public class Main extends Sprite
   {
      
      private var abfedb2fe4dd67ab:ScreenShotPanel;
      
      private var e7ffa9be95e4da5b603f:CursorSprite;
      
      private var afb49744073388b98:ToolTipSprite;
      
      public var a868f47895510ee8f7:String = "49941";
      
      private var e8381f477197d4:Number;
      
      private var bcdf5b2601a7a300fb:Number;
      
      private var cc7954a19b675eb3cd83f8e4c1a5:String;
      
      private var fc2ac6f23d8cbdee3f9:String;
      
      private var f87c1bf1a13132:String;
      
      private var e080670106d6d7ada4c3be:String;
      
      private var f78f73175559792164:String;
      
      private var cb275cc5b03a2e3:String;
      
      private var cda3562a0e8ef5f96ed:String;
      
      private var f7e22c95b831551:String;
      
      private var c95d31786f45e45f493a7:String;
      
      private var bf250cb54dc4bec9:String;
      
      private var cff82f42573e848:String;
      
      private var f95c78f157dc4fe6a475c:String;
      
      private var df7a393148ea2a8b6f:String;
      
      private var c57f853d351f7699ae:String;
      
      private var f3f3076bd1d1bb0a:String;
      
      private var cda1d8267c8e942a:String;
      
      private var ee8019c8834fffb:String;
      
      private var afe33b3f148b6d54:String;
      
      private var editComment:mc_editContent;
      
      private var _bmLayer:Sprite;
      
      private var _tmpImg:BitmapData;
      
      private var _sprite:Sprite;
      
      private var _oldRate:int;
      
      private var _pauseGame:Boolean;
      
      private var _pause:Boolean;
      
      private var cutNum:int = 0;
      
      private var ctrlObj:*;
      
      private var isDoCut:Boolean;
      
      internal var timerId:int = -1;
      
      public function Main()
      {
         super();
         if(stage)
         {
            this.init();
         }
         else
         {
            addEventListener(Event.ADDED_TO_STAGE,this.init);
         }
      }
      
      private function init(param1:Event = null) : void
      {
         var swf:SWFParser;
         var e:Event = param1;
         if(e)
         {
            removeEventListener(Event.ADDED_TO_STAGE,this.init);
         }
         Security.allowDomain("*");
         Security.allowInsecureDomain("*");
         this._oldRate = stage.frameRate;
         swf = new SWFParser(stage.getChildAt(0).loaderInfo.bytes);
         this.bcdf5b2601a7a300fb = swf.rect.height;
         this.e8381f477197d4 = swf.rect.width;
         trace("gameWid = " + this.e8381f477197d4);
         trace("gameHei = " + this.bcdf5b2601a7a300fb);
         stage.addEventListener("logreturn",this.onUserLogIn);
         this.ctrlObj = this.parent.parent;
         this.cc7954a19b675eb3cd83f8e4c1a5 = this.ddb416368e17ec1e([104,116,116,112,58,47,47,99,111,109,109,101,110,116,46,52,51,57,57,112,107,46,99,111,109,47,97,112,105,95,112,111,115,116,95,105,109,103,46,112,104,112,63]);
         this.f87c1bf1a13132 = this.ddb416368e17ec1e([67,111,110,116,101,110,116,45,116,121,112,101]);
         this.fc2ac6f23d8cbdee3f9 = this.ddb416368e17ec1e([97,112,112,108,105,99,97,116,105,111,110,47,111,99,116,101,116,45,115,116,114,101,97,109]);
         this.e080670106d6d7ada4c3be = this.ddb416368e17ec1e([52,51,57,57,99,111,109,109,101,110,116]);
         this.f78f73175559792164 = this.ddb416368e17ec1e([116,105,109,101,61]);
         this.cb275cc5b03a2e3 = this.ddb416368e17ec1e([38,102,105,100,61]);
         this.cda3562a0e8ef5f96ed = this.ddb416368e17ec1e([38,99,104,101,99,107,61]);
         this.f7e22c95b831551 = this.ddb416368e17ec1e([80,79,83,84]);
         this.c95d31786f45e45f493a7 = this.ddb416368e17ec1e([99,115,68,101,115,116,111,114,121,95,52,51,57,57]);
         this.bf250cb54dc4bec9 = this.ddb416368e17ec1e([99,117,116,115,104,111,116]);
         this.cff82f42573e848 = this.ddb416368e17ec1e([116,111,103,103,108,101,83,111,117,110,100]);
         this.f95c78f157dc4fe6a475c = this.ddb416368e17ec1e([116,111,103,103,108,101,71,97,109,101]);
         this.df7a393148ea2a8b6f = this.ddb416368e17ec1e([115,104,111,119,99,111,109,109,101,110,116]);
         this.c57f853d351f7699ae = this.ddb416368e17ec1e([102,97,105,108,117,114,101]);
         this.f3f3076bd1d1bb0a = this.ddb416368e17ec1e([79,75]);
         this.cda1d8267c8e942a = this.ddb416368e17ec1e([124]);
         if(ExternalInterface.available)
         {
            try
            {
               ExternalInterface.addCallback(this.bf250cb54dc4bec9,this.cutShoter);
               ExternalInterface.addCallback(this.cff82f42573e848,this.toggleSound);
               ExternalInterface.addCallback(this.f95c78f157dc4fe6a475c,this.toggleGame);
            }
            catch(e:Error)
            {
            }
         }
      }
      
      private function onUserLogIn(param1:Event) : void
      {
         if(this.isDoCut)
         {
            this.cutShoter();
         }
      }
      
      private function cutShoter() : void
      {
         if(getChildByName("bmLayer") != null)
         {
            return;
         }
         this.isDoCut = true;
         if(this.cutNum >= 2 && this.ctrlObj && this.ctrlObj.isLog == null)
         {
            this.ctrlObj.showLogPanel();
            return;
         }
         if(this._pauseGame)
         {
            this.gameContinueHandler();
            this._pause = true;
         }
         if(this.abfedb2fe4dd67ab == null)
         {
            if(this.e7ffa9be95e4da5b603f == null)
            {
               this.e7ffa9be95e4da5b603f = new CursorSprite();
            }
            stage.addChild(this.e7ffa9be95e4da5b603f);
            if(this.afb49744073388b98 == null)
            {
               this.afb49744073388b98 = new ToolTipSprite();
            }
            stage.addChild(this.afb49744073388b98);
            this.afb49744073388b98.visible = true;
            this.e7ffa9be95e4da5b603f.visible = true;
            this.abfedb2fe4dd67ab = new ScreenShotPanel(this.pictureCuttedHandler,stage,this.e8381f477197d4,this.bcdf5b2601a7a300fb);
            this.abfedb2fe4dd67ab.addEventListener(this.c95d31786f45e45f493a7,this.onDestory);
         }
      }
      
      private function onDestory(param1:Event) : void
      {
         this.afb49744073388b98.visible = false;
         this.e7ffa9be95e4da5b603f.visible = false;
         this.abfedb2fe4dd67ab.removeEventListener(this.c95d31786f45e45f493a7,this.onDestory);
         this.abfedb2fe4dd67ab = null;
      }
      
      private function pictureCuttedHandler(param1:BitmapData) : void
      {
         this._tmpImg = param1;
         this.afb49744073388b98.visible = false;
         this.e7ffa9be95e4da5b603f.visible = false;
         this.editComment = new mc_editContent();
         this.editComment.setImage(param1);
         this.editComment.addEventListener(EventManager.COMMENTCOMPLETED,this.commentOKHandler);
         this.editComment.addEventListener(EventManager.COMMENTCANCLE,this.commentCancleHandler);
         this._bmLayer = new Sprite();
         this._bmLayer.name = "bmLayer";
         this._bmLayer.blendMode = BlendMode.LAYER;
         this.addChild(this._bmLayer);
         var _loc2_:Shape = new Shape();
         _loc2_.graphics.beginFill(0,0.5);
         _loc2_.graphics.drawRect(0,0,this.e8381f477197d4,this.bcdf5b2601a7a300fb);
         _loc2_.graphics.endFill();
         this._bmLayer.addChild(_loc2_);
         this.editComment.x = this.e8381f477197d4 / 2 - this.editComment.width / 2;
         this.editComment.y = this.bcdf5b2601a7a300fb / 2 - this.editComment.height / 2;
         this._bmLayer.addChild(this.editComment);
      }
      
      private function commentOKHandler(param1:EventManager) : void
      {
         var _loc2_:String = this.editComment.getCommentConent();
         var _loc3_:BitmapData = this.editComment.getImageData();
         this.submitCommitHandler(this._tmpImg,_loc2_);
         var _loc4_:mc_submiting = new mc_submiting();
         _loc4_.addEventListener(EventManager.SAVECOMPLETED,this.saveCompletedHandler);
         _loc4_.addEventListener(EventManager.SAVING,this.savingHandler);
         _loc4_.x = this.e8381f477197d4 / 2 - _loc4_.width / 2;
         _loc4_.y = this.bcdf5b2601a7a300fb / 2 - _loc4_.height / 2;
         if(this._bmLayer)
         {
            _loc4_.startProgress(Math.random() * 5);
            this._bmLayer.addChild(_loc4_);
         }
         trace("评论填写完成，发送评论！");
      }
      
      private function commentCancleHandler(param1:EventManager) : void
      {
         if(getChildByName("bmLayer"))
         {
            this.removeChild(this._bmLayer);
         }
         this.isDoCut = false;
         trace("取消发送评论");
      }
      
      private function saveCompletedHandler(param1:EventManager) : void
      {
         var _loc2_:mc_completed = new mc_completed();
         _loc2_.addEventListener(EventManager.SUBMITCOMPLETED,this.submitCompletedHandler);
         _loc2_.x = this.e8381f477197d4 / 2 - _loc2_.width / 2;
         _loc2_.y = this.bcdf5b2601a7a300fb / 2 - _loc2_.height / 2;
         if(this._bmLayer)
         {
            this._bmLayer.addChild(_loc2_);
         }
         trace("上传完毕");
         ++this.cutNum;
      }
      
      private function savingHandler(param1:EventManager) : void
      {
         trace("上传中...");
      }
      
      private function submitCompletedHandler(param1:EventManager) : void
      {
         if(getChildByName("bmLayer"))
         {
            removeChild(getChildByName("bmLayer"));
         }
         this.isDoCut = false;
      }
      
      private function submitCommitHandler(param1:BitmapData, param2:String) : void
      {
         var _loc3_:JPEGEncoder = new JPEGEncoder(100);
         var _loc4_:ByteArray = _loc3_.encode(param1);
         var _loc5_:Number = Number(new Date().getTime());
         var _loc6_:String = this.a868f47895510ee8f7;
         var _loc7_:String = MD5.hash(MD5.hash(_loc5_ + this.e080670106d6d7ada4c3be + this.a868f47895510ee8f7));
         var _loc8_:* = _loc7_.slice(5,13);
         var _loc9_:String = "";
         _loc9_ = "time=" + _loc5_ + "&fid=" + _loc6_ + "&check=" + _loc8_ + "&content=" + param2;
         var _loc10_:URLRequestHeader = new URLRequestHeader("Content-type","application/octet-stream");
         trace("_header = " + _loc9_);
         trace("url = " + ("http://comment.4399pk.com/api_post_img_comm.php?" + _loc9_));
         var _loc11_:URLRequest = new URLRequest("http://comment.4399pk.com/api_post_img_comm.php?" + _loc9_);
         _loc11_.requestHeaders.push(_loc10_);
         _loc11_.method = "POST";
         _loc11_.data = _loc4_;
         var _loc12_:URLLoader = new URLLoader();
         this.configureListeners(_loc12_);
         _loc12_.load(_loc11_);
      }
      
      private function configureListeners(param1:IEventDispatcher) : void
      {
         param1.addEventListener(Event.COMPLETE,this.completeHandler);
         param1.addEventListener(Event.OPEN,this.openHandler);
         param1.addEventListener(ProgressEvent.PROGRESS,this.progressHandler);
         param1.addEventListener(SecurityErrorEvent.SECURITY_ERROR,this.securityErrorHandler);
         param1.addEventListener(HTTPStatusEvent.HTTP_STATUS,this.httpStatusHandler);
         param1.addEventListener(IOErrorEvent.IO_ERROR,this.ioErrorHandler);
      }
      
      private function completeHandler(param1:Event) : void
      {
         var _loc4_:String = null;
         var _loc5_:String = null;
         var _loc2_:String = String(param1.target.data);
         var _loc3_:Array = _loc2_.split(this.cda1d8267c8e942a);
         trace(_loc3_);
         if(_loc3_.length > 0)
         {
            if(_loc3_[0].toLocaleUpperCase() == this.f3f3076bd1d1bb0a)
            {
               _loc4_ = _loc3_[1];
               _loc5_ = _loc3_[2];
               trace("id:" + _loc4_ + ",tstr:" + _loc5_);
            }
            else if(_loc3_[0] == this.c57f853d351f7699ae)
            {
               trace("上传失败！");
            }
            else
            {
               trace("未知错误！");
            }
         }
      }
      
      private function openHandler(param1:Event) : void
      {
         trace("openHandler: " + param1);
      }
      
      private function progressHandler(param1:ProgressEvent) : void
      {
         trace("progressHandler loaded:" + param1.bytesLoaded + " total: " + param1.bytesTotal);
      }
      
      private function securityErrorHandler(param1:SecurityErrorEvent) : void
      {
         trace("securityErrorHandler: " + param1);
      }
      
      private function httpStatusHandler(param1:HTTPStatusEvent) : void
      {
         trace("httpStatusHandler: " + param1);
      }
      
      private function ioErrorHandler(param1:IOErrorEvent) : void
      {
         trace("ioErrorHandler: " + param1);
      }
      
      private function toggleSound(param1:Number) : void
      {
         var _loc2_:SoundTransform = SoundMixer.soundTransform;
         _loc2_.volume = param1 / 100;
         SoundMixer.soundTransform = _loc2_;
      }
      
      private function toggleGame(param1:Boolean) : void
      {
         if(param1)
         {
            this.gamePauseHandler();
         }
         else
         {
            this.gameContinueHandler();
         }
      }
      
      private function gamePauseHandler() : *
      {
         var _loc1_:MovieClip = null;
         if(this._sprite == null)
         {
            this._sprite = new Sprite();
            _loc1_ = new PauseImage();
            this._sprite.graphics.beginFill(13421772,0.6);
            this._sprite.graphics.drawRect(0,0,this.e8381f477197d4,this.bcdf5b2601a7a300fb);
            this._sprite.graphics.endFill();
            this._sprite.addChild(_loc1_);
            _loc1_.x = this.e8381f477197d4 / 2;
            _loc1_.y = this.bcdf5b2601a7a300fb / 2;
            this._sprite.buttonMode = true;
            this._sprite.mouseChildren = false;
         }
         stage.addChild(this._sprite);
         this._sprite.addEventListener(MouseEvent.CLICK,this.gameContinueHandler,false,0,true);
         this._pauseGame = true;
         if(this.timerId != -1)
         {
            clearTimeout(this.timerId);
            this.timerId = -1;
         }
         this.timerId = setTimeout(this.setFrameRateFun,150,0);
      }
      
      private function setFrameRateFun(param1:uint) : void
      {
         if(this.timerId != -1)
         {
            clearTimeout(this.timerId);
            this.timerId = -1;
         }
         stage.frameRate = param1;
      }
      
      private function gameContinueHandler(param1:MouseEvent = null) : *
      {
         if(param1)
         {
            this.callJsToSetBtnStatus(true);
         }
         stage.removeChild(this._sprite);
         this._sprite.removeEventListener(MouseEvent.CLICK,this.gameContinueHandler);
         this._pauseGame = false;
         stage.frameRate = this._oldRate;
      }
      
      private function callJsToSetBtnStatus(param1:Boolean) : void
      {
         var bol:Boolean = param1;
         if(ExternalInterface.available)
         {
            try
            {
               ExternalInterface.call("setGameStatus",bol);
            }
            catch(e:Event)
            {
            }
         }
      }
      
      private function ddb416368e17ec1e(param1:Array) : String
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:String = null;
         if(param1 == null)
         {
            return null;
         }
         _loc3_ = int(param1.length);
         _loc4_ = "";
         _loc2_ = 0;
         while(_loc2_ < _loc3_)
         {
            _loc4_ += String.fromCharCode(param1[_loc2_]);
            _loc2_++;
         }
         return _loc4_;
      }
   }
}

