package ghostcat.parse.graphics
{
   import flash.display.Graphics;
   import ghostcat.parse.DisplayParse;
   
   public class GraphicsLineStyle extends DisplayParse implements IGraphicsLineStyle
   {
      
      public var thickness:Number;
      
      public var color:uint;
      
      public var alpha:Number;
      
      public var pixelHinting:Boolean;
      
      public var scaleMode:String;
      
      public var caps:String;
      
      public var joints:String;
      
      public var miterLimit:Number;
      
      public function GraphicsLineStyle(param1:Number = NaN, param2:uint = 0, param3:Number = 1, param4:Boolean = false, param5:String = "normal", param6:String = null, param7:String = null, param8:Number = 3)
      {
         super();
         this.thickness = param1;
         this.color = param2;
         this.alpha = param3;
         this.pixelHinting = param4;
         this.scaleMode = param5;
         this.caps = param6;
         this.joints = param7;
         this.miterLimit = param8;
      }
      
      override public function parseGraphics(param1:Graphics) : void
      {
         super.parseGraphics(param1);
         param1.lineStyle(this.thickness,this.color,this.alpha,this.pixelHinting,this.scaleMode,this.caps,this.joints,this.miterLimit);
      }
   }
}

