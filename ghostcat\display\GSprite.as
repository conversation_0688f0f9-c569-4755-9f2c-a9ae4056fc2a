package ghostcat.display
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.Loader;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.utils.getDefinitionByName;
   import ghostcat.events.GEvent;
   import ghostcat.util.core.ClassFactory;
   
   public class GSprite extends Sprite implements IDisplayObjectContainer
   {
      
      private var _content:DisplayObject;
      
      private var _replace:Boolean = true;
      
      public var destoryWhenRemove:Boolean = false;
      
      public var autoDestoryContent:Boolean = true;
      
      public var resizeContent:Boolean = true;
      
      public var initialized:Boolean;
      
      public var destoryed:Boolean = false;
      
      public var acceptContentPosition:<PERSON>olean = true;
      
      private var contentInited:Boolean = false;
      
      private var _hideContent:Boolean;
      
      public function GSprite(param1:* = null, param2:Boolean = true)
      {
         super();
         addEventListener(Event.ADDED_TO_STAGE,this.addedToStageHandler);
         addEventListener(Event.REMOVED_FROM_STAGE,this.removedFromStageHandler);
         this.setContent(param1,param2);
      }
      
      public function get replace() : Boolean
      {
         return this._replace;
      }
      
      public function set replace(param1:Boolean) : void
      {
         this._replace = param1;
         this.setContent(this._content,param1);
      }
      
      public function get skin() : *
      {
         return this._content;
      }
      
      public function set skin(param1:*) : void
      {
         this.setContent(param1,this.replace);
      }
      
      public function get content() : DisplayObject
      {
         return this._content;
      }
      
      public function set content(param1:DisplayObject) : void
      {
         this._content = param1;
      }
      
      public function get hideContent() : Boolean
      {
         return this._hideContent;
      }
      
      public function set hideContent(param1:Boolean) : void
      {
         if(!this.content)
         {
            return;
         }
         this._hideContent = param1;
         if(param1)
         {
            if(this.content.parent == this)
            {
               removeChild(this.content);
            }
         }
         else if(this.content.parent != this)
         {
            addChild(this.content);
         }
      }
      
      override public function set width(param1:Number) : void
      {
         if(Boolean(this.content) && this.resizeContent)
         {
            this.content.width = param1;
         }
         else
         {
            super.width = param1;
         }
      }
      
      override public function set height(param1:Number) : void
      {
         if(Boolean(this.content) && this.resizeContent)
         {
            this.content.height = param1;
         }
         else
         {
            super.height = param1;
         }
      }
      
      public function setContent(param1:*, param2:Boolean = true) : void
      {
         var _loc3_:int = 0;
         var _loc4_:DisplayObjectContainer = null;
         if(param1 is String)
         {
            param1 = getDefinitionByName(param1 as String);
         }
         if(param1 is Class)
         {
            param1 = new ClassFactory(param1);
         }
         if(param1 is ClassFactory)
         {
            param1 = (param1 as ClassFactory).newInstance();
         }
         if(this._content == param1)
         {
            return;
         }
         if(param1 is BitmapData)
         {
            param1 = new Bitmap(param1 as BitmapData);
         }
         if(Boolean(this._content) && this._content.parent == this)
         {
            if(this._content is IGBase && this.autoDestoryContent)
            {
               (this._content as IGBase).destory();
            }
            if(this._content.parent)
            {
               this.$removeChild(this._content);
            }
         }
         if(param2 && param1)
         {
            if(this.acceptContentPosition && !this.contentInited)
            {
               this.x = param1.x;
               this.y = param1.y;
               param1.x = param1.y = 0;
            }
            if(this._content == null)
            {
               if(param1.parent)
               {
                  _loc4_ = param1.parent;
                  _loc3_ = int(param1.parent.getChildIndex(param1));
               }
            }
            this.$addChild(param1);
            this.visible = param1.visible;
            param1.visible = true;
            this.name = param1.name;
         }
         this._content = param1;
         if(_loc4_ && !(_loc4_ is Loader) && _loc4_ != this)
         {
            _loc4_.addChildAt(this,_loc3_);
         }
         this.contentInited = true;
      }
      
      private function addedToStageHandler(param1:Event) : void
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.addedToStageHandler);
         this.init();
         this.initialized = true;
         dispatchEvent(new GEvent(GEvent.CREATE_COMPLETE));
      }
      
      private function removedFromStageHandler(param1:Event) : void
      {
         if(this.destoryWhenRemove)
         {
            this.removeEventListener(Event.REMOVED_FROM_STAGE,this.removedFromStageHandler);
            this.destory();
         }
      }
      
      protected function init() : void
      {
      }
      
      public function destory() : void
      {
         if(this.destoryed)
         {
            return;
         }
         removeEventListener(Event.ADDED_TO_STAGE,this.addedToStageHandler);
         removeEventListener(Event.REMOVED_FROM_STAGE,this.removedFromStageHandler);
         if(this.content is IGBase && this.autoDestoryContent)
         {
            (this.content as IGBase).destory();
         }
         if(parent)
         {
            parent.removeChild(this);
         }
         this.destoryed = true;
      }
      
      public function $addChild(param1:DisplayObject) : DisplayObject
      {
         return super.addChild(param1);
      }
      
      public function $addChildAt(param1:DisplayObject, param2:int) : DisplayObject
      {
         return super.addChildAt(param1,param2);
      }
      
      public function $removeChild(param1:DisplayObject) : DisplayObject
      {
         return super.removeChild(param1);
      }
      
      public function $removeChildAt(param1:int) : DisplayObject
      {
         return super.removeChildAt(param1);
      }
      
      public function $getChildAt(param1:int) : DisplayObject
      {
         return super.getChildAt(param1);
      }
      
      public function $getChildByName(param1:String) : DisplayObject
      {
         return super.getChildByName(param1);
      }
      
      public function $getChildIndex(param1:DisplayObject) : int
      {
         return super.getChildIndex(param1);
      }
   }
}

