package ghostcat.display.screenshot
{
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.Stage;
   import flash.geom.Rectangle;
   import ghostcat.parse.display.DrawParse;
   import ghostcat.util.display.BitmapUtil;
   
   public final class ScreenShotUtil
   {
      
      public function ScreenShotUtil()
      {
         super();
      }
      
      public static function shotObject(param1:DisplayObject, param2:Rectangle = null, param3:Array = null) : BitmapData
      {
         if(!param3)
         {
            param3 = [];
         }
         var _loc4_:Array = [];
         var _loc5_:int = 0;
         while(_loc5_ < param3.length)
         {
            _loc4_.push(param3[_loc5_].visible);
            param3[_loc5_].visible = false;
            _loc5_++;
         }
         var _loc6_:DrawParse = new DrawParse(param1);
         _loc6_.clipRect = param2;
         var _loc7_:BitmapData = _loc6_.createBitmapData();
         if(param2)
         {
            _loc7_ = BitmapUtil.clip(_loc7_,param2,true);
         }
         _loc5_ = 0;
         while(_loc5_ < param3.length)
         {
            param3[_loc5_].visible = _loc4_[_loc5_].visible;
            _loc5_++;
         }
         return _loc7_;
      }
      
      public static function showScreen(param1:Stage, param2:Number, param3:Number, param4:Array = null) : BitmapData
      {
         if(!param4)
         {
            param4 = [];
         }
         var _loc5_:Array = [];
         var _loc6_:int = 0;
         while(_loc6_ < param4.length)
         {
            _loc5_.push(param4[_loc6_].visible);
            param4[_loc6_].visible = false;
            _loc6_++;
         }
         var _loc7_:BitmapData = new BitmapData(param2,param3,false);
         _loc7_.draw(param1);
         _loc6_ = 0;
         while(_loc6_ < param4.length)
         {
            param4[_loc6_].visible = _loc5_[_loc6_].visible;
            _loc6_++;
         }
         return _loc7_;
      }
   }
}

