package ghostcat.ui.tooltip
{
   import flash.display.BlendMode;
   import flash.display.DisplayObject;
   import ghostcat.skin.ArowSkin;
   import ghostcat.ui.ToolTipSprite;
   import ghostcat.ui.controls.GText;
   import ghostcat.ui.layout.Padding;
   import ghostcat.util.core.ClassFactory;
   import ghostcat.util.easing.TweenUtil;
   
   public class ToolTipSkin extends GText implements IToolTipSkin
   {
      
      public static var defaultSkin:ClassFactory = new ClassFactory(ArowSkin);
      
      public function ToolTipSkin(param1:* = null)
      {
         if(!param1)
         {
            param1 = defaultSkin;
         }
         super(param1,true,true,new Padding(2,2,2,2));
         this.enabledAdjustContextSize = true;
      }
      
      public function show(param1:DisplayObject) : void
      {
         var _loc2_:ToolTipSprite = this.parent as ToolTipSprite;
         _loc2_.x = _loc2_.parent.mouseX + 10;
         _loc2_.y = _loc2_.parent.mouseY + 10;
         _loc2_.blendMode = BlendMode.LAYER;
         TweenUtil.removeTween(_loc2_);
         TweenUtil.from(_loc2_,100,{
            "alpha":0,
            "y":"10"
         }).update();
      }
      
      public function positionTo(param1:DisplayObject) : void
      {
         var _loc2_:ToolTipSprite = null;
         TweenUtil.removeTween(_loc2_);
         _loc2_ = this.parent as ToolTipSprite;
         _loc2_.x = _loc2_.parent.mouseX + 10;
         _loc2_.y = _loc2_.parent.mouseY + 10;
      }
   }
}

