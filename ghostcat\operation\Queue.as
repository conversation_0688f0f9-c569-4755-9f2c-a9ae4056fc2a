package ghostcat.operation
{
   import flash.events.Event;
   import ghostcat.debug.Debug;
   import ghostcat.events.OperationEvent;
   
   public class Queue extends Oper
   {
      
      private static var _defaultQueue:Queue;
      
      public var children:Array = [];
      
      public function Queue(param1:Array = null)
      {
         super();
         if(!param1)
         {
            param1 = [];
         }
         this.children = param1;
      }
      
      public static function get defaultQueue() : Queue
      {
         if(!_defaultQueue)
         {
            _defaultQueue = new Queue();
         }
         return _defaultQueue;
      }
      
      public function commitChild(param1:Oper) : void
      {
         param1.queue = this;
         param1.step = Oper.WAIT;
         this.children.push(param1);
         if(this.children.length == 1)
         {
            this.doLoad();
         }
      }
      
      public function haltChild(param1:Oper) : void
      {
         param1.queue = null;
         param1.step = Oper.NONE;
         var _loc2_:int = int(this.children.indexOf(param1));
         if(_loc2_ == -1)
         {
            return;
         }
         if(_loc2_ == 0)
         {
            this.nexthandler();
         }
         else
         {
            this.children.splice(_loc2_,1);
         }
      }
      
      private function doLoad() : void
      {
         var _loc1_:Oper = null;
         if(this.children.length > 0)
         {
            _loc1_ = this.children[0];
            _loc1_.addEventListener(OperationEvent.OPERATION_COMPLETE,this.nexthandler);
            _loc1_.addEventListener(OperationEvent.OPERATION_ERROR,this.nexthandler);
            _loc1_.execute();
         }
         else
         {
            result();
         }
      }
      
      private function nexthandler(param1:Event = null) : void
      {
         var _loc2_:Oper = this.children[0] as Oper;
         _loc2_.removeEventListener(OperationEvent.OPERATION_COMPLETE,this.nexthandler);
         _loc2_.removeEventListener(OperationEvent.OPERATION_ERROR,this.nexthandler);
         this.children.shift();
         if(_loc2_.continueWhenFail || param1.type == OperationEvent.OPERATION_COMPLETE)
         {
            this.doLoad();
         }
      }
      
      override public function commit(param1:Queue = null) : void
      {
         if(!param1)
         {
            param1 = Queue.defaultQueue;
         }
         if(param1 == this)
         {
            Debug.error("不能将自己推入自己的队列中");
         }
         else
         {
            super.commit(param1);
         }
      }
      
      override public function execute() : void
      {
         super.execute();
         this.doLoad();
      }
      
      override public function halt() : void
      {
         super.halt();
         if(this.children.length > 0)
         {
            this.children = this.children.slice(0,1);
            (this.children[0] as Oper).halt();
         }
      }
   }
}

