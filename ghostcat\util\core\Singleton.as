package ghostcat.util.core
{
   import flash.errors.IllegalOperationError;
   import flash.events.EventDispatcher;
   import flash.utils.Dictionary;
   import flash.utils.getQualifiedClassName;
   
   public class Singleton extends EventDispatcher
   {
      
      private static var dict:Dictionary = new Dictionary();
      
      public function Singleton()
      {
         super();
         var _loc1_:Class = this["constructor"] as Class;
         if(dict[_loc1_])
         {
            throw new IllegalOperationError(getQualifiedClassName(this) + " 只允许实例化一次！");
         }
         dict[_loc1_] = this;
      }
      
      public static function getInstance(param1:Class) : *
      {
         return dict[param1];
      }
      
      public static function getInstanceOrCreate(param1:Class) : *
      {
         if(dict[param1] == null)
         {
            dict[param1] = new param1();
         }
         return dict[param1];
      }
      
      public static function create(param1:Class) : *
      {
         dict[param1] = new param1();
         return dict[param1];
      }
      
      public function destory() : void
      {
         var _loc1_:Class = this["constructor"] as Class;
         delete dict[_loc1_];
      }
   }
}

