package ghostcat.display
{
   import flash.display.DisplayObject;
   import flash.geom.Rectangle;
   import ghostcat.util.display.Geom;
   
   public class GNoScale extends GBase
   {
      
      private var _height:Number;
      
      private var _width:Number;
      
      public var enabledAutoSize:Boolean = true;
      
      public function GNoScale(param1:* = null, param2:Boolean = true)
      {
         super(param1,param2);
         this.sizeCall.frame = false;
      }
      
      override public function setContent(param1:*, param2:Boolean = true) : void
      {
         super.setContent(param1,param2);
         if(this.enabledAutoSize)
         {
            this.autoSize();
         }
      }
      
      public function autoSize() : void
      {
         if(content)
         {
            this._width = content.width;
            this._height = content.height;
         }
      }
      
      override public function get width() : Number
      {
         return this._width ? this._width : super.width;
      }
      
      override public function set width(param1:Number) : void
      {
         if(this._width == param1)
         {
            return;
         }
         this._width = param1;
         invalidateSize();
      }
      
      override public function get height() : Number
      {
         return this._height ? this._height : super.height;
      }
      
      override public function set height(param1:Number) : void
      {
         if(this._height == param1)
         {
            return;
         }
         this._height = param1;
         invalidateSize();
      }
      
      public function get sized() : <PERSON>olean
      {
         return Boolean(this._height) || Boolean(this._width);
      }
      
      override public function setSize(param1:Number, param2:Number, param3:Boolean = false) : void
      {
         if(this._width == param1 && this._height == param2)
         {
            return;
         }
         this._width = param1;
         this._height = param2;
         vaildSize(param3);
      }
      
      override public function getRect(param1:DisplayObject) : Rectangle
      {
         if(this.sized)
         {
            return Geom.localRectToContent(new Rectangle(0,0,this.width,this.height),this,param1);
         }
         return super.getRect(param1);
      }
      
      override protected function updateSize() : void
      {
         super.updateSize();
         updateDisplayList();
      }
   }
}

