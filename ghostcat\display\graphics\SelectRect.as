package ghostcat.display.graphics
{
   import flash.display.DisplayObjectContainer;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import ghostcat.display.GBase;
   import ghostcat.events.SelectEvent;
   import ghostcat.parse.graphics.GraphicsFill;
   import ghostcat.parse.graphics.GraphicsLineStyle;
   import ghostcat.parse.graphics.IGraphicsFill;
   import ghostcat.parse.graphics.IGraphicsLineStyle;
   
   public class SelectRect extends GBase
   {
      
      public var lineStyle:IGraphicsLineStyle;
      
      public var fill:IGraphicsFill;
      
      private var start:Point;
      
      private var end:Point;
      
      private var _rectWid:Number;
      
      private var _rectHei:Number;
      
      private var _startX:Number;
      
      private var _startY:Number;
      
      private var _endX:Number;
      
      private var _endY:Number;
      
      public var rHandler:Function;
      
      public function SelectRect(param1:Number, param2:Number, param3:Function = null, param4:IGraphicsLineStyle = null, param5:IGraphicsFill = null)
      {
         super();
         this._rectHei = param2;
         this._rectWid = param1;
         mouseEnabled = mouseChildren = false;
         if(!param5)
         {
            param5 = new GraphicsFill(0);
         }
         if(!param4)
         {
            param4 = new GraphicsLineStyle(3,16711680);
         }
         this.fill = param5;
         this.lineStyle = param4;
         this.rHandler = param3;
      }
      
      public function createTo(param1:DisplayObjectContainer) : void
      {
         param1.addChild(this);
         stage.addEventListener(MouseEvent.MOUSE_DOWN,this.mouseDownHandler);
      }
      
      private function mouseDownHandler(param1:MouseEvent) : void
      {
         this.show();
      }
      
      public function show() : void
      {
         this._startX = mouseX;
         this._startY = mouseY;
         if(this._startX <= 0)
         {
            this._startX = 0;
         }
         else if(this._startX >= this._rectWid)
         {
            this._startX = this._rectWid;
         }
         if(this._startY <= 0)
         {
            this._startY = 0;
         }
         else if(this._startY >= this._rectHei)
         {
            this._startY = this._rectHei;
         }
         this.start = new Point(this._startX,this._startY);
         this.end = this.start.clone();
         stage.addEventListener(MouseEvent.MOUSE_MOVE,this.mouseMoveHandler);
         stage.addEventListener(MouseEvent.MOUSE_UP,this.mouseUpHandler);
         this.refresh();
      }
      
      private function mouseMoveHandler(param1:MouseEvent) : void
      {
         this._endX = mouseX;
         this._endY = mouseY;
         if(this._endX <= 0)
         {
            this._endX = 0;
         }
         else if(this._endX >= this._rectWid)
         {
            this._endX = this._rectWid;
         }
         if(this._endY <= 0)
         {
            this._endY = 0;
         }
         else if(this._endY >= this._rectHei)
         {
            this._endY = this._rectHei;
         }
         this.end = new Point(this._endX,this._endY);
         this.refresh();
      }
      
      private function mouseUpHandler(param1:MouseEvent) : void
      {
         stage.removeEventListener(MouseEvent.MOUSE_MOVE,this.mouseMoveHandler);
         stage.removeEventListener(MouseEvent.MOUSE_UP,this.mouseUpHandler);
         var _loc2_:Number = Math.min(this.start.x,this.end.x);
         var _loc3_:Number = Math.min(this.start.y,this.end.y);
         var _loc4_:Number = Math.max(this.start.x,this.end.x);
         var _loc5_:Number = Math.max(this.start.y,this.end.y);
         var _loc6_:Rectangle = new Rectangle(_loc2_,_loc3_,_loc4_ - _loc2_,_loc5_ - _loc3_);
         var _loc7_:SelectEvent = new SelectEvent(SelectEvent.SELECT);
         _loc7_.rect = _loc6_;
         dispatchEvent(_loc7_);
         if(this.rHandler != null)
         {
            this.rHandler(_loc6_);
         }
         this.start = this.end = null;
         this.refresh();
      }
      
      public function refresh() : void
      {
         graphics.clear();
         if(Boolean(this.start) && Boolean(this.end))
         {
            this.lineStyle.parse(this);
            this.fill.parse(this);
            graphics.drawRect(this.start.x,this.start.y,this.end.x - this.start.x,this.end.y - this.start.y);
         }
      }
      
      override public function destory() : void
      {
         if(destoryed)
         {
            return;
         }
         stage.removeEventListener(MouseEvent.MOUSE_MOVE,this.mouseMoveHandler);
         stage.removeEventListener(MouseEvent.MOUSE_UP,this.mouseUpHandler);
         stage.removeEventListener(MouseEvent.MOUSE_DOWN,this.mouseDownHandler);
         super.destory();
      }
   }
}

