package ghostcat.parse.graphics
{
   import flash.display.Graphics;
   import flash.geom.Point;
   import ghostcat.parse.DisplayParse;
   import ghostcat.util.MathUtil;
   
   public class GraphicsRect extends DisplayParse
   {
      
      public var x:Number;
      
      public var y:Number;
      
      public var width:Number;
      
      public var height:Number;
      
      public var topLeftRadius:Number = NaN;
      
      public var topRightRadius:Number = NaN;
      
      public var bottomLeftRadius:Number = NaN;
      
      public var bottomRightRadius:Number = NaN;
      
      public var arowWidth:Number = 10;
      
      public var arowTo:Point;
      
      public var arowFrom:Point;
      
      public function GraphicsRect(param1:Number, param2:Number, param3:Number, param4:Number, param5:Number = NaN, param6:Number = NaN, param7:Number = NaN, param8:Number = NaN, param9:Point = null, param10:Number = 10, param11:Point = null)
      {
         super();
         this.x = param1;
         this.y = param2;
         this.width = param3;
         this.height = param4;
         this.topLeftRadius = param5;
         this.topRightRadius = param6;
         this.bottomLeftRadius = param7;
         this.bottomRightRadius = param8;
         this.arowWidth = param10;
         this.arowTo = param9;
         this.arowFrom = param11;
      }
      
      public static function drawRoundRectComplex(param1:Graphics, param2:Number, param3:Number, param4:Number, param5:Number, param6:Number, param7:Number, param8:Number, param9:Number, param10:Point = null, param11:Number = 10, param12:Point = null) : void
      {
         var _loc22_:Number = NaN;
         var _loc13_:Number = Math.min(param4 * 2,param5 * 2);
         param6 = Math.min(!isNaN(param6) ? param6 : 0,_loc13_);
         param7 = Math.min(!isNaN(param7) ? param7 : 0,_loc13_);
         param8 = Math.min(!isNaN(param8) ? param8 : 0,_loc13_);
         param9 = Math.min(!isNaN(param9) ? param9 : 0,_loc13_);
         var _loc14_:Number = param2 + param4;
         var _loc15_:Number = param3 + param5;
         var _loc16_:int = -1;
         if(!param12 && Boolean(param10))
         {
            param12 = param10.clone();
         }
         if(param12)
         {
            if(param12.y > _loc15_)
            {
               _loc16_ = 0;
               param12.x = MathUtil.limitIn(param12.x,param2 + param8 + param11 / 2,_loc14_ - param9 - param11 / 2);
            }
            else if(param12.x < param2)
            {
               _loc16_ = 1;
               param12.y = MathUtil.limitIn(param12.y,param3 + param6 + param11 / 2,_loc15_ - param8 - param11 / 2);
            }
            else if(param12.y < param3)
            {
               _loc16_ = 2;
               param12.x = MathUtil.limitIn(param12.x,param2 + param6 + param11 / 2,_loc14_ - param7 - param11 / 2);
            }
            else if(param12.x > _loc14_)
            {
               _loc16_ = 3;
               param12.y = MathUtil.limitIn(param12.y,param3 + param7 + param11 / 2,_loc15_ - param9 - param11 / 2);
            }
            _loc22_ = param11 / 2;
         }
         var _loc17_:Array = [];
         var _loc18_:Number = 0.292893218813453;
         var _loc19_:Number = 0.585786437626905;
         var _loc20_:Number = param9 * _loc18_;
         var _loc21_:Number = param9 * _loc19_;
         param1.moveTo(_loc14_,_loc15_ - param9);
         param1.curveTo(_loc14_,_loc15_ - _loc21_,_loc14_ - _loc20_,_loc15_ - _loc20_);
         param1.curveTo(_loc14_ - _loc21_,_loc15_,_loc14_ - param9,_loc15_);
         if(_loc16_ == 0)
         {
            param1.lineTo(param12.x + _loc22_,_loc15_);
            param1.lineTo(param10.x,param10.y);
            param1.lineTo(param12.x - _loc22_,_loc15_);
         }
         _loc20_ = param8 * _loc18_;
         _loc21_ = param8 * _loc19_;
         param1.lineTo(param2 + param8,_loc15_);
         param1.curveTo(param2 + _loc21_,_loc15_,param2 + _loc20_,_loc15_ - _loc20_);
         param1.curveTo(param2,_loc15_ - _loc21_,param2,_loc15_ - param8);
         if(_loc16_ == 1)
         {
            param1.lineTo(param2,param12.y + _loc22_);
            param1.lineTo(param10.x,param10.y);
            param1.lineTo(param2,param12.y - _loc22_);
         }
         _loc20_ = param6 * _loc18_;
         _loc21_ = param6 * _loc19_;
         param1.lineTo(param2,param3 + param6);
         param1.curveTo(param2,param3 + _loc21_,param2 + _loc20_,param3 + _loc20_);
         param1.curveTo(param2 + _loc21_,param3,param2 + param6,param3);
         if(_loc16_ == 2)
         {
            param1.lineTo(param12.x - _loc22_,param3);
            param1.lineTo(param10.x,param10.y);
            param1.lineTo(param12.x + _loc22_,param3);
         }
         _loc20_ = param7 * _loc18_;
         _loc21_ = param7 * _loc19_;
         param1.lineTo(_loc14_ - param7,param3);
         param1.curveTo(_loc14_ - _loc21_,param3,_loc14_ - _loc20_,param3 + _loc20_);
         param1.curveTo(_loc14_,param3 + _loc21_,_loc14_,param3 + param7);
         if(_loc16_ == 3)
         {
            param1.lineTo(_loc14_,param12.y - _loc22_);
            param1.lineTo(param10.x,param10.y);
            param1.lineTo(_loc14_,param12.y + _loc22_);
         }
         param1.lineTo(_loc14_,_loc15_ - param9);
      }
      
      override public function parseGraphics(param1:Graphics) : void
      {
         var _loc2_:Number = NaN;
         super.parseGraphics(param1);
         if(this.arowTo)
         {
            drawRoundRectComplex(param1,this.x,this.y,this.width,this.height,this.topLeftRadius,this.topRightRadius,this.bottomLeftRadius,this.bottomRightRadius,this.arowTo,this.arowWidth,this.arowFrom);
         }
         else if(isNaN(this.topLeftRadius) && isNaN(this.topRightRadius) && isNaN(this.bottomLeftRadius) && isNaN(this.bottomRightRadius))
         {
            param1.drawRect(this.x,this.y,this.width,this.height);
         }
         else if(this.topLeftRadius && isNaN(this.topRightRadius) && isNaN(this.bottomLeftRadius) && isNaN(this.bottomRightRadius))
         {
            _loc2_ = Math.min(this.width / 2,this.height / 2);
            this.topLeftRadius = Math.min(this.topLeftRadius,_loc2_);
            this.topRightRadius = Math.min(this.topLeftRadius,_loc2_);
            param1.drawRoundRect(this.x,this.y,this.width,this.height,this.topLeftRadius * 2,this.topLeftRadius * 2);
         }
         else
         {
            drawRoundRectComplex(param1,this.x,this.y,this.width,this.height,this.topLeftRadius,this.topRightRadius,this.bottomLeftRadius,this.bottomRightRadius,this.arowTo,this.arowWidth,this.arowFrom);
         }
      }
   }
}

