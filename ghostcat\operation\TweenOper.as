package ghostcat.operation
{
   import ghostcat.operation.effect.IEffect;
   import ghostcat.util.ReflectUtil;
   import ghostcat.util.easing.TweenEvent;
   import ghostcat.util.easing.TweenUtil;
   
   public class TweenOper extends Oper implements IEffect
   {
      
      protected var _target:*;
      
      public var duration:int;
      
      public var params:Object;
      
      public var updateWhenInvent:Boolean = true;
      
      public var clearTarget:Boolean;
      
      public var tween:TweenUtil;
      
      public function TweenOper(param1:* = null, param2:int = 100, param3:Object = null, param4:Boolean = false, param5:Boolean = false, param6:Boolean = false)
      {
         super();
         this._target = param1;
         this.duration = param2;
         this.params = param3;
         this.immediately = param6;
         if(param4)
         {
            this.invert = param4;
         }
         this.clearTarget = param5;
      }
      
      public function get target() : *
      {
         return this._target;
      }
      
      public function set target(param1:*) : void
      {
         this._target = param1;
      }
      
      public function get invert() : Bo<PERSON>an
      {
         return this.params ? Boolean(this.params.invert) : false;
      }
      
      public function set invert(param1:Boolean) : void
      {
         if(!this.params)
         {
            this.params = new Object();
         }
         this.params.invert = param1;
      }
      
      override public function execute() : void
      {
         var _loc1_:* = undefined;
         super.execute();
         if(this._target is String)
         {
            _loc1_ = ReflectUtil.eval(this._target);
         }
         else
         {
            _loc1_ = this._target;
         }
         if(this.clearTarget)
         {
            TweenUtil.removeTween(_loc1_);
         }
         this.tween = new TweenUtil(_loc1_,this.duration,this.params);
         this.tween.addEventListener(TweenEvent.TWEEN_END,result);
         if(this.invert && this.updateWhenInvent)
         {
            this.tween.update();
         }
      }
      
      override protected function end(param1:* = null) : void
      {
         if(this.tween)
         {
            this.tween.removeEventListener(TweenEvent.TWEEN_END,result);
         }
         super.end(param1);
      }
   }
}

