package ghostcat.display.graphics
{
   import flash.display.DisplayObject;
   import flash.display.Shape;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.Dictionary;
   import ghostcat.display.GBase;
   import ghostcat.events.MoveEvent;
   import ghostcat.parse.display.RectParse;
   import ghostcat.parse.graphics.GraphicsFill;
   import ghostcat.parse.graphics.GraphicsLineStyle;
   import ghostcat.parse.graphics.GraphicsRect;
   import ghostcat.ui.CursorSprite;
   
   public class ScaleBox extends GBase
   {
      
      private static var objs:Dictionary = new Dictionary();
      
      public var fillControl:DragPoint;
      
      public var topLeftControl:DragPoint;
      
      public var topRightControl:DragPoint;
      
      public var bottomLeftControl:DragPoint;
      
      public var bottomRightControl:DragPoint;
      
      public var topLineControl:DragPoint;
      
      public var bottomLineControl:DragPoint;
      
      public var leftLineControl:DragPoint;
      
      public var rightLineControl:DragPoint;
      
      private var _lockMove:Boolean;
      
      private var _dragWid:Number;
      
      private var _dragHei:Number;
      
      private var fill:GraphicsFill = new GraphicsFill(0,0);
      
      private var lineStyle:GraphicsLineStyle = new GraphicsLineStyle();
      
      public function ScaleBox(param1:DisplayObject, param2:Number, param3:Number)
      {
         super();
         this._dragWid = param2;
         this._dragHei = param3;
         this.createControl();
         this.content = param1;
         this.selected = true;
      }
      
      public static function register(param1:DisplayObject, param2:Number, param3:Number) : void
      {
         objs[param1] = new ScaleBox(param1,param2,param3);
      }
      
      public static function unregister(param1:DisplayObject) : void
      {
         objs[param1].destory();
         delete objs[param1];
      }
      
      public function get lockMove() : Boolean
      {
         return this._lockMove;
      }
      
      public function set lockMove(param1:Boolean) : void
      {
         this._lockMove = param1;
         this.fillControl.visible = !param1;
      }
      
      override public function set selected(param1:Boolean) : void
      {
         super.selected = param1;
         if(param1)
         {
            content.parent.addChild(this);
            this.updateControls();
         }
         else
         {
            content.parent.removeChild(this);
         }
      }
      
      public function updateControls() : void
      {
         var _loc1_:Rectangle = content.getBounds(content.parent);
         this.fillControl.setPosition(new Point(_loc1_.x,_loc1_.y),true);
         new RectParse(new GraphicsRect(2,2,_loc1_.width - 4,_loc1_.height - 4),this.lineStyle,this.fill,null,true).parse(this.fillControl.content);
         this.topLeftControl.setPosition(new Point(_loc1_.x,_loc1_.y),true);
         new RectParse(new GraphicsRect(-2,-2,4,4),this.lineStyle,this.fill,null,true).parse(this.topLeftControl.content);
         this.topRightControl.setPosition(new Point(_loc1_.right,_loc1_.y),true);
         new RectParse(new GraphicsRect(-2,-2,4,4),this.lineStyle,this.fill,null,true).parse(this.topRightControl.content);
         this.bottomLeftControl.setPosition(new Point(_loc1_.x,_loc1_.bottom),true);
         new RectParse(new GraphicsRect(-2,-2,4,4),this.lineStyle,this.fill,null,true).parse(this.bottomLeftControl.content);
         this.bottomRightControl.setPosition(new Point(_loc1_.right,_loc1_.bottom),true);
         new RectParse(new GraphicsRect(-2,-2,4,4),this.lineStyle,this.fill,null,true).parse(this.bottomRightControl.content);
         this.topLineControl.setPosition(new Point(_loc1_.x,_loc1_.y),true);
         new RectParse(new GraphicsRect(2,-2,_loc1_.width - 4,4),this.lineStyle,this.fill,null,true).parse(this.topLineControl.content);
         this.bottomLineControl.setPosition(new Point(_loc1_.x,_loc1_.bottom),true);
         new RectParse(new GraphicsRect(2,-2,_loc1_.width - 4,4),this.lineStyle,this.fill,null,true).parse(this.bottomLineControl.content);
         this.leftLineControl.setPosition(new Point(_loc1_.x,_loc1_.y),true);
         new RectParse(new GraphicsRect(-2,2,4,_loc1_.height - 4),this.lineStyle,this.fill,null,true).parse(this.leftLineControl.content);
         this.rightLineControl.setPosition(new Point(_loc1_.right,_loc1_.y),true);
         new RectParse(new GraphicsRect(-2,2,4,_loc1_.height - 4),this.lineStyle,this.fill,null,true).parse(this.rightLineControl.content);
      }
      
      private function createControl() : void
      {
         this.fillControl = new DragPoint(new Shape());
         this.fillControl.cursor = CursorSprite.CURSOR_DRAG;
         this.fillControl.addEventListener(MoveEvent.MOVE,this.fillMouseDownHandler,false,0,true);
         addChild(this.fillControl);
         this.topLeftControl = new DragPoint(new Shape());
         this.topLeftControl.cursor = CursorSprite.CURSOR_HV_DRAG;
         this.topLeftControl.addEventListener(MoveEvent.MOVE,this.topLeftControlHandler,false,0,true);
         addChild(this.topLeftControl);
         this.topRightControl = new DragPoint(new Shape());
         this.topRightControl.cursor = CursorSprite.CURSOR_VH_DRAG;
         this.topRightControl.addEventListener(MoveEvent.MOVE,this.topRightControlHandler,false,0,true);
         addChild(this.topRightControl);
         this.bottomLeftControl = new DragPoint(new Shape());
         this.bottomLeftControl.cursor = CursorSprite.CURSOR_VH_DRAG;
         this.bottomLeftControl.addEventListener(MoveEvent.MOVE,this.bottomLeftControlHandler,false,0,true);
         addChild(this.bottomLeftControl);
         this.bottomRightControl = new DragPoint(new Shape());
         this.bottomRightControl.cursor = CursorSprite.CURSOR_HV_DRAG;
         this.bottomRightControl.addEventListener(MoveEvent.MOVE,this.bottomRightControlHandler,false,0,true);
         addChild(this.bottomRightControl);
         this.topLineControl = new DragPoint(new Shape());
         this.topLineControl.cursor = CursorSprite.CURSOR_V_DRAG;
         this.topLineControl.lockX = true;
         this.topLineControl.addEventListener(MoveEvent.MOVE,this.topLineControlHandler,false,0,true);
         addChild(this.topLineControl);
         this.bottomLineControl = new DragPoint(new Shape());
         this.bottomLineControl.cursor = CursorSprite.CURSOR_V_DRAG;
         this.bottomLineControl.lockX = true;
         this.bottomLineControl.addEventListener(MoveEvent.MOVE,this.bottomLineControlHandler,false,0,true);
         addChild(this.bottomLineControl);
         this.leftLineControl = new DragPoint(new Shape());
         this.leftLineControl.cursor = CursorSprite.CURSOR_H_DRAG;
         this.leftLineControl.lockY = true;
         this.leftLineControl.addEventListener(MoveEvent.MOVE,this.leftLineControlHandler,false,0,true);
         addChild(this.leftLineControl);
         this.rightLineControl = new DragPoint(new Shape());
         this.rightLineControl.cursor = CursorSprite.CURSOR_H_DRAG;
         this.rightLineControl.lockY = true;
         this.rightLineControl.addEventListener(MoveEvent.MOVE,this.rightLineControlHandler,false,0,true);
         addChild(this.rightLineControl);
      }
      
      private function topLineControlHandler(param1:MoveEvent) : void
      {
         if(!this.topLineControl.mouseDown)
         {
            return;
         }
         var _loc2_:Rectangle = content.getBounds(content.parent);
         var _loc3_:Number = this.topLineControl.position.y - _loc2_.y;
         if(content.y + _loc3_ <= 0)
         {
            _loc3_ = 0 - content.y;
         }
         else if(content.y + _loc3_ >= content.height + content.y)
         {
            _loc3_ = content.height - 2;
         }
         content.y += _loc3_;
         content.height -= _loc3_;
         this.updateControls();
      }
      
      private function bottomLineControlHandler(param1:MoveEvent) : void
      {
         if(!this.bottomLineControl.mouseDown)
         {
            return;
         }
         var _loc2_:Number = this.bottomLineControl.position.y - this.topLineControl.position.y;
         if(_loc2_ + content.y >= this._dragHei)
         {
            _loc2_ = this._dragHei - content.y;
         }
         else if(_loc2_ + content.y <= content.y)
         {
            _loc2_ = 2;
         }
         content.height = _loc2_;
         this.updateControls();
      }
      
      private function leftLineControlHandler(param1:MoveEvent) : void
      {
         if(!this.leftLineControl.mouseDown)
         {
            return;
         }
         var _loc2_:Rectangle = content.getBounds(content.parent);
         var _loc3_:Number = this.leftLineControl.position.x - _loc2_.x;
         if(content.x + _loc3_ <= 0)
         {
            _loc3_ = 0 - content.x;
         }
         else if(content.x + _loc3_ >= content.x + content.width)
         {
            _loc3_ = content.width - 2;
         }
         content.x += _loc3_;
         content.width -= _loc3_;
         this.updateControls();
      }
      
      private function rightLineControlHandler(param1:MoveEvent) : void
      {
         var _loc2_:Number = NaN;
         if(!this.rightLineControl.mouseDown)
         {
            return;
         }
         _loc2_ = this.rightLineControl.position.x - this.leftLineControl.x;
         if(_loc2_ <= 0)
         {
            _loc2_ = 2;
         }
         else if(_loc2_ + content.x >= this._dragWid)
         {
            _loc2_ = this._dragWid - content.x;
         }
         content.width = _loc2_;
         this.updateControls();
      }
      
      private function topLeftControlHandler(param1:MoveEvent) : void
      {
         if(!this.topLeftControl.mouseDown)
         {
            return;
         }
         var _loc2_:Rectangle = content.getBounds(content.parent);
         var _loc3_:Number = this.topLeftControl.position.x - _loc2_.x;
         if(content.x + _loc3_ <= 0)
         {
            _loc3_ = 0 - content.x;
         }
         else if(content.x + _loc3_ >= content.x + content.width)
         {
            _loc3_ = content.width - 2;
         }
         content.x += _loc3_;
         content.width -= _loc3_;
         var _loc4_:Number = this.topLeftControl.position.y - _loc2_.y;
         if(content.y + _loc4_ <= 0)
         {
            _loc4_ = 0 - content.y;
         }
         else if(content.y + _loc4_ >= content.y + content.height)
         {
            _loc4_ = content.height - 2;
         }
         content.y += _loc4_;
         content.height -= _loc4_;
         this.updateControls();
      }
      
      private function topRightControlHandler(param1:MoveEvent) : void
      {
         if(!this.topRightControl.mouseDown)
         {
            return;
         }
         var _loc2_:Rectangle = content.getBounds(content.parent);
         var _loc3_:Number = this.topRightControl.position.y - _loc2_.y;
         if(content.y + _loc3_ <= 0)
         {
            _loc3_ = 0 - content.y;
         }
         else if(content.y + _loc3_ >= content.y + content.height)
         {
            _loc3_ = content.height - 2;
         }
         content.y += _loc3_;
         content.height -= _loc3_;
         var _loc4_:Number = this.topRightControl.position.x - this.leftLineControl.x;
         if(_loc4_ + content.x >= this._dragWid)
         {
            _loc4_ = this._dragWid - content.x;
         }
         else if(_loc4_ + content.x <= content.x)
         {
            _loc4_ = 2;
         }
         content.width = _loc4_;
         this.updateControls();
      }
      
      private function bottomLeftControlHandler(param1:MoveEvent) : void
      {
         if(!this.bottomLeftControl.mouseDown)
         {
            return;
         }
         var _loc2_:Rectangle = content.getBounds(content.parent);
         var _loc3_:Number = this.bottomLeftControl.position.x - _loc2_.x;
         if(_loc3_ + content.x <= 0)
         {
            _loc3_ = 0 - content.x;
         }
         else if(_loc3_ + content.x >= content.x + content.width)
         {
            _loc3_ = content.width - 2;
         }
         content.x += _loc3_;
         content.width -= _loc3_;
         var _loc4_:Number = this.bottomLeftControl.position.y - this.topLineControl.position.y;
         if(_loc4_ + content.y >= this._dragHei)
         {
            _loc4_ = this._dragHei - content.y;
         }
         else if(_loc4_ + content.y <= content.y)
         {
            _loc4_ = 2;
         }
         content.height = _loc4_;
         this.updateControls();
      }
      
      private function bottomRightControlHandler(param1:MoveEvent) : void
      {
         var _loc2_:Number = NaN;
         var _loc3_:Number = NaN;
         if(!this.bottomRightControl.mouseDown)
         {
            return;
         }
         _loc2_ = this.bottomRightControl.position.x - this.leftLineControl.x;
         _loc3_ = this.bottomRightControl.position.y - this.topLineControl.position.y;
         if(_loc2_ + content.x >= this._dragWid)
         {
            _loc2_ = this._dragWid - content.x;
         }
         else if(_loc2_ + content.x <= content.x)
         {
            _loc2_ = 2;
         }
         if(_loc3_ + content.y >= this._dragHei)
         {
            _loc3_ = this._dragHei - content.y;
         }
         else if(_loc3_ + content.y <= content.y)
         {
            _loc3_ = 2;
         }
         content.height = _loc3_;
         content.width = _loc2_;
         this.updateControls();
      }
      
      private function fillMouseDownHandler(param1:MoveEvent) : void
      {
         if(!this.fillControl.mouseDown)
         {
            return;
         }
         var _loc2_:Rectangle = content.getBounds(content.parent);
         var _loc3_:Number = content.x;
         var _loc4_:Number = content.y;
         _loc3_ += this.fillControl.x - _loc2_.x;
         _loc4_ += this.fillControl.y - _loc2_.y;
         if(_loc3_ <= 0)
         {
            _loc3_ = 0;
         }
         else if(_loc3_ + content.width >= this._dragWid)
         {
            _loc3_ = this._dragWid - content.width;
         }
         if(_loc4_ <= 0)
         {
            _loc4_ = 0;
         }
         else if(_loc4_ + content.height >= this._dragHei)
         {
            _loc4_ = this._dragHei - content.height;
         }
         content.x = _loc3_;
         content.y = _loc4_;
         this.updateControls();
      }
      
      override public function destory() : void
      {
         this.content = null;
         super.destory();
      }
   }
}

