package ghostcat.parse.display
{
   import flash.display.DisplayObjectContainer;
   import flash.geom.Point;
   import flash.text.TextField;
   import flash.text.TextFieldAutoSize;
   import flash.text.TextFormat;
   import ghostcat.parse.DisplayParse;
   
   public class TextFieldParse extends DisplayParse
   {
      
      public var text:String;
      
      public var pos:Point;
      
      public var defaultTextFormat:TextFormat;
      
      public function TextFieldParse(param1:String = null, param2:Point = null, param3:TextFormat = null)
      {
         super();
         this.text = param1 ? param1 : "";
         this.pos = param2;
         this.defaultTextFormat = param3;
      }
      
      public static function createTextField(param1:String = "", param2:Point = null, param3:TextFormat = null) : TextField
      {
         var _loc4_:TextFieldParse = new TextFieldParse(param1,param2,param3);
         return _loc4_.createTextField();
      }
      
      override public function parseContainer(param1:DisplayObjectContainer) : void
      {
         super.parseContainer(param1);
         param1.addChild(this.createTextField());
      }
      
      public function createTextField() : TextField
      {
         if(!this.defaultTextFormat)
         {
            this.defaultTextFormat = new TextFormat("宋体",12);
         }
         var _loc1_:TextField = new TextField();
         _loc1_.defaultTextFormat = this.defaultTextFormat;
         _loc1_.selectable = false;
         _loc1_.autoSize = TextFieldAutoSize.LEFT;
         if(this.text.indexOf("<html>") != -1)
         {
            _loc1_.htmlText = this.text;
         }
         else
         {
            _loc1_.text = this.text;
         }
         if(this.pos)
         {
            _loc1_.x = this.pos.x;
            _loc1_.y = this.pos.y;
         }
         _loc1_.autoSize = TextFieldAutoSize.LEFT;
         return _loc1_;
      }
   }
}

