package ghostcat.util.display
{
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public final class DisplayUtil
   {
      
      public function DisplayUtil()
      {
         super();
      }
      
      public static function inScreen(param1:DisplayObject) : Boolean
      {
         if(param1.stage == null)
         {
            return false;
         }
         var _loc2_:Rectangle = Geom.getRect(param1.stage);
         return _loc2_.containsRect(param1.getBounds(param1.stage));
      }
      
      public static function addChildAfter(param1:DisplayObject, param2:DisplayObject) : void
      {
         param2.parent.addChildAt(param1,param2.parent.getChildIndex(param2) + 1);
      }
      
      public static function addChildBefore(param1:DisplayObject, param2:DisplayObject) : void
      {
         param2.parent.addChildAt(param1,param2.parent.getChildIndex(param2));
      }
      
      public static function getChildren(param1:DisplayObjectContainer) : Array
      {
         var _loc2_:Array = [];
         var _loc3_:int = 0;
         while(_loc3_ < param1.numChildren)
         {
            _loc2_.push(param1.getChildAt(_loc3_));
            _loc3_++;
         }
         return _loc2_;
      }
      
      public static function removeAllChildren(param1:DisplayObjectContainer) : void
      {
         while(param1.numChildren)
         {
            param1.removeChildAt(0);
         }
      }
      
      public static function addAllChildren(param1:DisplayObjectContainer, param2:Array) : void
      {
         var _loc3_:int = 0;
         while(_loc3_ < param2.length)
         {
            if(param2[_loc3_] is Array)
            {
               addAllChildren(param1,param2[_loc3_] as Array);
            }
            else
            {
               param1.addChild(param2[_loc3_]);
            }
            _loc3_++;
         }
      }
      
      public static function moveToHigh(param1:DisplayObject) : void
      {
         var _loc3_:int = 0;
         var _loc2_:DisplayObjectContainer = param1.parent;
         if(_loc2_)
         {
            _loc3_ = _loc2_.numChildren - 1;
            if(_loc2_.getChildIndex(param1) < _loc3_)
            {
               _loc2_.setChildIndex(param1,_loc3_);
            }
         }
      }
      
      public static function setMouseEnabled(param1:DisplayObjectContainer, param2:Boolean) : void
      {
         param1.mouseChildren = param1.mouseEnabled = param2;
      }
      
      public static function cloneDisplayObject(param1:DisplayObject) : DisplayObject
      {
         var _loc2_:DisplayObject = param1["constructor"]();
         _loc2_.filters = _loc2_.filters;
         _loc2_.transform.colorTransform = param1.transform.colorTransform;
         _loc2_.transform.matrix = param1.transform.matrix;
         if(_loc2_ is Bitmap)
         {
            (_loc2_ as Bitmap).bitmapData = (param1 as Bitmap).bitmapData;
         }
         return _loc2_;
      }
      
      public static function getStageRotation(param1:DisplayObject) : Number
      {
         var _loc2_:DisplayObject = param1;
         var _loc3_:Number = 1;
         while(Boolean(_loc2_) && _loc2_.parent != _loc2_)
         {
            _loc3_ += _loc2_.rotation;
            _loc2_ = _loc2_.parent;
         }
         return _loc3_;
      }
      
      public static function getStageScale(param1:DisplayObject) : Point
      {
         var _loc2_:DisplayObject = param1;
         var _loc3_:Point = new Point(1,1);
         while(Boolean(_loc2_) && _loc2_.parent != _loc2_)
         {
            _loc3_.x *= _loc2_.scaleX;
            _loc3_.y *= _loc2_.scaleY;
            _loc2_ = _loc2_.parent;
         }
         return _loc3_;
      }
      
      public static function getStageVisible(param1:DisplayObject) : Boolean
      {
         var _loc2_:DisplayObject = param1;
         while(Boolean(_loc2_) && _loc2_.parent != _loc2_)
         {
            if(_loc2_.visible == false)
            {
               return false;
            }
            _loc2_ = _loc2_.parent;
         }
         return true;
      }
   }
}

