package swfParser
{
   public class Tag
   {
      
      public var type:int;
      
      public var position:uint;
      
      public var length:uint;
      
      public var data:Data;
      
      public function Tag(param1:uint = 0, param2:uint = 0, param3:uint = 0)
      {
         super();
         this.type = param1;
         this.position = param2;
         this.length = param3;
      }
      
      public function toString() : String
      {
         return "Tag:" + this.type + "," + this.data;
      }
   }
}

