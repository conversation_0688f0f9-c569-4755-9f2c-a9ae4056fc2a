package ghostcat.util.display
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.Shape;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public final class BitmapUtil
   {
      
      public function BitmapUtil()
      {
         super();
      }
      
      public static function drawToBitmap(param1:DisplayObject) : BitmapData
      {
         var _loc2_:Rectangle = param1.getBounds(param1);
         var _loc3_:Matrix = new Matrix();
         _loc3_.translate(-_loc2_.x,-_loc2_.y);
         var _loc4_:BitmapData = new BitmapData(_loc2_.width,_loc2_.height,true,0);
         _loc4_.draw(param1,_loc3_);
         return _loc4_;
      }
      
      public static function scale(param1:BitmapData, param2:Number = 1, param3:Number = 1, param4:Boolean = true) : BitmapData
      {
         var _loc5_:BitmapData = new BitmapData(param1.width * param2,param1.height * param3,param1.transparent);
         var _loc6_:Matrix = new Matrix();
         _loc6_.scale(param2,param3);
         _loc5_.draw(param1,_loc6_);
         if(param4)
         {
            param1.dispose();
         }
         return _loc5_;
      }
      
      public static function clip(param1:BitmapData, param2:Rectangle, param3:Boolean = true) : BitmapData
      {
         var _loc4_:BitmapData = new BitmapData(param2.width,param2.height,param1.transparent);
         _loc4_.copyPixels(param1,param2,new Point());
         if(param3)
         {
            param1.dispose();
         }
         return _loc4_;
      }
      
      public static function clear(param1:BitmapData) : void
      {
         param1.fillRect(param1.rect,0);
      }
      
      public static function getMask(param1:BitmapData) : Shape
      {
         var _loc4_:int = 0;
         var _loc2_:Shape = new Shape();
         _loc2_.graphics.beginFill(0);
         var _loc3_:int = 0;
         while(_loc3_ < param1.width)
         {
            _loc4_ = 0;
            while(_loc4_ < param1.height)
            {
               if(param1.getPixel32(_loc3_,_loc4_))
               {
                  _loc2_.graphics.drawRect(_loc3_,_loc4_,1,1);
               }
               _loc4_++;
            }
            _loc3_++;
         }
         _loc2_.graphics.endFill();
         return _loc2_;
      }
      
      public static function dispose(param1:Array) : void
      {
         var _loc2_:* = undefined;
         for each(_loc2_ in param1)
         {
            if(_loc2_ is BitmapData)
            {
               (_loc2_ as BitmapData).dispose();
            }
            if(_loc2_ is Bitmap)
            {
               (_loc2_ as Bitmap).bitmapData.dispose();
               if((_loc2_ as Bitmap).parent)
               {
                  (_loc2_ as Bitmap).parent.removeChild(_loc2_ as Bitmap);
               }
            }
         }
      }
   }
}

